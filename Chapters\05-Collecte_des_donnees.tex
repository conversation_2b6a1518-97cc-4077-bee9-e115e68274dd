\titlespacing*{\chapter}{0pt}{-10pt}{40pt}
\chapter{Collecte des données}
\label{cp:collecte des donnees}
Pour développer un modèle efficace de détection de vulnérabilités, une étape cruciale consiste à constituer un ensemble de données pertinent. Dans cette étude, 1 009 commits de correction de vulnérabilités ont été extraits de différents dépôts GitHub. Ces commits représentent des modifications apportées au code pour remédier à des failles de sécurité.
\par
\noindent De plus, les auteurs ont mis à disposition les données collectées sur la plateforme Zenodo, permettant ainsi la reproductibilité de leur étude et la collaboration au sein de la communauté de recherche. Sur ce site, on peut accéder aux fichiers en texte brut contenant les commits de correction de vulnérabilités extraits.\\

\noindent Les types de vulnérabilités ciblées incluent l'injection SQL, le Cross-Site Scripting (XSS), l'injection de commandes, le Cross-Site Request Forgery (XSRF), l'exécution de code à distance, la divulgation de chemins et les redirections ouvertes. Cette sélection assure une couverture diversifiée des vulnérabilités courantes et critiques, ce qui renforce la robustesse du modèle lors de son entraînement.