\titlespacing*{\chapter}{0pt}{-10pt}{40pt}
\chapter{Résultats expérimentaux}
\label{cp:resultats experimentaux}
% Dans l'évaluation expérimentale de VUDENC, l'outil a démontré une efficacité notable dans la détection des vulnérabilités au sein de projets Python réels. En analysant un corpus de 1 009 commits de correction de vulnérabilités issus de divers dépôts GitHub, VUDENC a ciblé avec succès sept types de vulnérabilités, notamment les injections SQL, les failles XSS et l'exécution de code à distance. Les résultats obtenus indiquent que VUDENC atteint un équilibre solide entre le rappel et la précision, avec un F1-score se situant entre 80\% et 90\%. Cette performance témoigne de la capacité de l'outil à identifier non seulement la présence de vulnérabilités, mais aussi à localiser précisément les fragments de code concernés.\\

% Un aspect remarquable des résultats est la réduction significative des faux positifs et des faux négatifs, ce qui est crucial pour minimiser les efforts des développeurs lors de l'analyse et de la correction du code. De plus, VUDENC fournit des niveaux de confiance associés à chaque prédiction, permettant aux équipes de développement de prioriser les vulnérabilités en fonction de leur criticité potentielle. L'approche innovante de VUDENC, combinant l'embedding word2vec pour capturer les relations sémantiques entre les tokens et les réseaux LSTM pour modéliser les séquences de code, a prouvé son efficacité lors de cette évaluation.\\

% Ces résultats suggèrent que VUDENC surpasse les outils existants en offrant une détection fine et précise des vulnérabilités sur du code réel, ce qui en fait une contribution significative dans le domaine de la sécurité logicielle. L'outil se distingue par sa capacité à généraliser à travers différents projets sans nécessiter de configuration spécifique, tout en maintenant des performances élevées qui facilitent son adoption dans des environnements de développement variés.

Le tableau ci-dessous présente les résultats de VUDENC sur différents types de vulnérabilités. Pour chaque type de vulnérabilité, on a le nombre d'exemples utilisés pour l'entraînement et les tests, le pourcentage de code vulnérable détecté, ainsi que différentes mesures de performance comme la précision, le rappel et le F1-score.

\begin{table}[h]
\centering
\caption{Résultats pour chaque type de vulnérabilité. Les colonnes sont : \textbf{Train (exemples d'entraînement)}, \textbf{Test (exemples de test)}, \textbf{Vul (\% de code vulnérable)}, \textbf{Acc (\% d'exactitude)}, \textbf{Prec (\% de précision)}, \textbf{Rec (\% de rappel)}, et \textbf{F1 (F1-Score)} \cite{DBLP:journals/corr/abs-2201-08441}. }
\label{tab:vul_results}
\begin{tabular}{lrrrrrrr} 
\toprule
\textbf{Vulnérabilité} & \textbf{Train} & \textbf{Test} & \textbf{Vul (\%)} & \textbf{Acc (\%)} & \textbf{Prec (\%)} & \textbf{Rec (\%)} & \textbf{F1 (\%)}  \\ 
\midrule
SQL injection          & 42,690 & 8,196 & 19.2 & 92.5 & 82.2 & 78.0 & 80.1 \\
XSS                    & 8,277  & 717   & 8.7  & 97.8 & 91.9 & 80.8 & 86.0 \\
Command injection      & 18,814 & 2,287 & 12.2 & 97.8 & 94.0 & 87.2 & 90.5 \\
XSRF                   & 27,434 & 3,600 & 13.1 & 97.2 & 92.9 & 85.4 & 89.0 \\
Remote code execution  & 14,412 & 1,303 & 9.0  & 98.1 & 96.0 & 82.2 & 88.8 \\
Path disclosure        & 19,680 & 2,315 & 11.8 & 97.3 & 92.0 & 84.4 & 88.1 \\
Open redirect          & 12,740 & 1,691 & 13.3 & 96.8 & 91.0 & 83.9 & 87.3 \\
\bottomrule
\end{tabular}
\end{table}

Le modèle affiche une précision globale élevée, variant entre 92,5\% et 98,1\% selon le type de vulnérabilité. En termes de précision, les scores oscillent entre 82,2\% pour les injections SQL et 96,0\% pour l'exécution de code à distance, indiquant une variabilité dans la capacité du modèle à identifier correctement les instances vulnérables selon la nature de la faille. Le rappel, qui mesure la capacité du modèle à détecter les vulnérabilités réelles, varie de 78,0\% pour les injections SQL à 87,2\% pour les injections de commandes. Le score F1, combinant précision et rappel, se situe entre 80,1\% et 90,5\%, reflétant des performances globales différentes selon les types de vulnérabilités analysés.\\

La distribution des données d'entraînement et de test montre que les injections SQL représentent la part la plus élevée de code vulnérable (19,2\%), tandis que l'exécution de code à distance est la moins fréquente (9,0\%). Certaines vulnérabilités, comme les injections de commandes et les exécutions de code à distance, bénéficient d'un nombre modéré d'exemples d'entraînement et de test, ce qui peut influencer les performances observées. Par exemple, les injections de commandes obtiennent le meilleur score F1 avec 90,5\%, tandis que les injections SQL présentent les performances les plus faibles avec un score F1 de 80,1\%. Ces variations peuvent être attribuées à la diversité des exemples disponibles et à la complexité inhérente à la détection de certaines vulnérabilités.\\

Les différences observées entre les types de vulnérabilités suggèrent que certaines catégories peuvent nécessiter des ajustements supplémentaires ou un enrichissement des données d'entraînement pour améliorer les performances globales du modèle.