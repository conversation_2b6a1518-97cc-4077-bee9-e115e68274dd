{
  "source": "from flask import ...",
  "sourceWithComments": "from flask import ...",
  "/common/json_schema.py": {
    "changes": [
      {
        "diff": "last_sign_in_ip = ...",
        "add": 6,
        "remove": 6,
        "filename": "/common/json_schema.py",
        "badparts": ["created_at = 
        fields.DateTime(required=True)"],
        "goodparts": ["created_at = 
        fields.DateTime(dump_only=True, required=True)"]
      },
    ]
  }
}
