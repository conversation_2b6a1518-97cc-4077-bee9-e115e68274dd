\ifthenelse{\equal{\getLanguage}{portuguese}}{%
    \addtocontents{toc}{\protect\contentsline{chapter}{Apêndices}{}{}}
}{%
    \addtocontents{toc}{\protect\contentsline{chapter}{Appendices}{}{}}
}

\blankpage\newgeometry{margin=4cm}
\begin{center}
    \scshape % Font style
    \thispagestyle{empty}
        
    \vspace*{\fill}
    \ifthenelse{\equal{\getLanguage}{portuguese}}{%
        {\LARGE\fontsize{20}{26}\selectfont\textcolor{darkred}{Apêndices}\par}
    }{%
        {\LARGE\fontsize{20}{26}\selectfont\textcolor{darkred}{Appendices}\par}
    }
    \vspace*{\fill}
\end{center}
\restoregeometry\blankpage