\titlespacing*{\chapter}{0pt}{-10pt}{40pt}
\chapter{Reproduction}
\label{cp:reproduction}



\section{Environnement d’exécution}
La reproduction de l’article a rencontré plusieurs défis importants liés à l’environnement d’exécution. Le dépôt Git \cite{wartschinski_vulnerability_2023} utilisé, élément essentiel de l’expérience, n’a pas été maintenu depuis cinq ans. Cette obsolescence a imposé l’utilisation de Python 3.6.13 et de versions anciennes de bibliothèques Python, souvent incompatibles avec les standards actuels. Pour surmonter ces contraintes, un environnement virtuel a été créé avec Conda, permettant de reproduire cet environnement daté en versionnant 121 bibliothèques Python. La machine utilisée dispose de 12 cores, d’un processeur Xeon® Gold 5118 et de 64 Go de RAM.

\section{Préparation des datasets}
Les datasets fournis par les auteurs n’étaient pas directement exploitables. Disponibles sous format JSON, ils nécessitaient un processus de parsing intensif, consommant beaucoup de mémoire et de temps, suivi d’une étape de vectorisation. Ce processus a pris environ 1h30. De plus, le modèle Word2Vec préentraîné mis à disposition sur le dépôt Git ne pouvait pas être chargé correctement. Nous avons donc dû entraîner un nouveau modèle Word2Vec en nous basant sur les données disponibles sur Zenodo \cite{zenodo3559480} et les scripts Python fournis. Bien que la possibilité de créer un dataset personnalisé existât, nous avons choisi d’utiliser les données des auteurs pour garantir la compatibilité avec les étapes suivantes de la reproduction. 

\section{Entraînement du modèle Word2Vec}
Le processus d’entraînement du modèle Word2Vec a été particulièrement exigeant. Le dataset brut devait passer par un nettoyage approfondi, incluant la gestion de symboles arithmétiques comme \texttt{+}, \texttt{-}, \texttt{/} et \texttt{*}, très répandus dans les codes logiciels. Ces symboles, ignorés dans la version nettoyée du dataset, auraient pu induire des interprétations incorrectes des relations sémantiques. Après ces étapes, les données ont été tokenisées pour générer des représentations vectorielles compatibles avec le modèle Word2Vec. Les paramètres suggérés par l’article, comme la dimensionnalité des vecteurs et le nombre d’itérations, ont été respectés. Les résultats obtenus étaient proches de ceux annoncés, confirmant l’efficacité de cette étape malgré les contraintes. Le temps d'entraînement pour le modèle Word2Vec a pris environ 2 h.

\begin{verbatim}
>>> from gensim.models import Word2Vec, KeyedVectors
>>> model = Word2Vec.load("w2v/word2vec_withString10-100-200.model")

>>> model.wv.most_similar("if")
[('elif', 0.8235), ('assert', 0.6755), ('and', 0.5553), ...]

>>> model.wv.most_similar("count")
[('len', 0.5127), ('num', 0.4786), ('total', 0.4754), ...]

>>> model.wv.most_similar("split")
[('lstrip', 0.6413), ('rstrip', 0.6405), ('rsplit', 0.6178), ...]

>>> model.wv.most_similar("x")
[('y', 0.7471), ('z', 0.5951), ('v', 0.5196), ...]

>>> model.wv.most_similar("+")
[('+=', 0.6325), ('/', 0.5815), ('*', 0.5299), ...]
\end{verbatim}
\captionof{listing}{Extraits des similarités calculées avec le modèle Word2Vec pour différents mots de requête.}





\section{Entraînement du modèle LSTM}
L’entraînement du modèle LSTM a présenté ses propres défis. Bien que ces problèmes soient davantage liés aux datasets qu’au modèle en lui-même, ils ont nécessité des ajustements importants. Les fichiers JSON contenant des exemples de vulnérabilités devaient d’abord être parsés, un processus coûteux en mémoire qui dépassait parfois les limites autorisées dans nos infrastructures (CREMI). Après parsing, un étiquetage a été réalisé via une bibliothèque dédiée incluse dans le code des auteurs. Ce processus permettait de marquer les portions vulnérables et non vulnérables dans un contexte limité à cinq caractères.

\vspace{0.5cm}

Le modèle LSTM a été conçu avec une architecture séquentielle composée de 100 neurones, un dropout de 0.2 pour limiter le surapprentissage, et une couche Dense avec activation sigmoïde pour la sortie. Les hyperparamètres incluaient un batch size de 128, un optimiseur Adam, et un entraînement sur 50 époques pour minimiser les temps de calcul. Sur 50 époques, l'entraînement dure environ 3h30.



\begin{figure}[H]
    \centering
    \includegraphics[width=0.8\textwidth]{Figures/loss_and_f1_same_graph.png}
    \vspace{0.5cm}
    \caption{Évolution de la loss et du F1 score sur 50 epochs.}
    \label{fig:lstm_results}
\end{figure}





Malgré ces ajustements, les modèles LSTM préentraînés par les auteurs ne produisaient pas les mêmes résultats que ceux annoncés. Après avoir activé des conditions de filtrage initialement désactivées (comme l’exclusion des commits modifiant trop de lignes ou de fichiers), et après un entraînement sur 22 époques pour le dataset XSS (Cross-Site Scripting), nous avons atteint un F1-score d’environ 80 \%, accompagné d’un rendu visuel nettement amélioré. L'entraînement sur 22 époques a pris environ 1h.

\begin{figure}[h!]
    \centering
    \begin{minipage}{0.45\textwidth}
        \centering
        \includegraphics[width=\textwidth]{Figures/demo_xss_4_.png}
                \vspace{0.5cm}
        \caption{LSTM Avant}
        \label{fig:LSTM_output_avant}
    \end{minipage}\hfill
    \begin{minipage}{0.45\textwidth}
        \centering
        \includegraphics[width=\textwidth]{Figures/demo_xss_1_.png}
                        \vspace{0.5cm}

        \caption{LSTM Après}
        \label{fig:LSTM_output_apres}
    \end{minipage}
\end{figure}


\section{Résultats sur la détection de vulnérabilités Remote Code Execution (RCE)}
En complément, nous avons entraîné un modèle dédié à la détection de vulnérabilités de type Remote Code Execution (RCE). 





Voici une comparaison entre leurs résultats et les nôtres pour la commande \texttt{python3 demonstrate.py remote\_code\_execution 1 fine} :

\begin{figure}[H]
    \centering
    \includegraphics[width=\textwidth]{Figures/eux.png}
    \vspace{0.5cm}
    \caption{Leur résultat (produit par \texttt{python3 demonstrate.py remote\_code\_execution 1 fine}).}
    \label{fig:leur_resultat}
\end{figure}

\begin{figure}[H]
    \centering
    \includegraphics[width=\textwidth]{Figures/demo_remote_code_execution_1_-2.png}
    \vspace{0.5cm}
    \caption{Notre résultat (produit par \texttt{python3 demonstrate.py remote\_code\_execution 1 fine}).}
    \label{fig:notre_resultat}
\end{figure}

Les résultats obtenus sont les suivants :

% \begin{table}[h!]
% \centering
% \begin{tabular}{|l|c|c|c|c|}
% \hline
% \textbf{Dataset}   & \textbf{Accuracy} & \textbf{Precision} & \textbf{Recall} & \textbf{F1 score} \\ \hline
% Train set          & 0.9807            & 0.9741             & 0.6896          & 0.8075            \\ \hline
% Validation set           & 0.9782            & 0.9675             & 0.6766          & 0.7963            \\ \hline
% Final test set     & 0.9792            & 0.9675             & 0.6717          & 0.7929            \\ \hline
% \end{tabular}
% \caption{Validation des données sur les ensembles d'entraînement, de validation et test }
% \label{tab:validation_results}
% \end{table}

\begin{table}[h!]
    \centering
    \begin{tabular}{|l|c|c|}
        \hline
        \textbf{Metric}   & \textbf{Leurs résultats} & \textbf{Nos résultats} \\ \hline
        \textbf{Accuracy} & 98.1\%                                & 97.92\%                             \\ \hline
        \textbf{Precision}& 96.0\%                                & 96.75\%                             \\ \hline
        \textbf{Recall}   & 82.2\%                                & 67.17\%                             \\ \hline
        \textbf{F1 Score} & 88.8\%                                & 79.29\%                             \\ \hline
        \textbf{Epochs}   & 100                                   & 50                                  \\ \hline
    \end{tabular}
    \caption{Comparaison des résultats pour le Remote Code Execution dataset.}
\end{table}


Ces performances, bien qu'imparfaites sur le rappel, montrent une forte précision et un F1-score globalement satisfaisant pour la détection des vulnérabilités RCE. Ces résultats valident en partie l’approche proposée tout en soulignant l’importance d’un dataset robuste et équilibré.

\section{Limitations et défis}
Les principales limitations incluent la dépendance aux datasets fournis par les auteurs, nécessitant des mises à jour fréquentes pour rester pertinents face à l’évolution des logiciels. Les processus de parsing et d’entraînement sont coûteux en temps et en ressources, limitant leur applicabilité sur des machines modestes. 

De plus, il existe un déséquilibre significatif entre les classes dans le dataset, avec une majorité de données marquées comme non vulnérables et une minorité marquée comme vulnérables. Cela rend difficile la généralisation du modèle, affectant particulièrement les métriques telles que le rappel et le F1-score pour la classe minoritaire.

Enfin, la méthode reste dépendante d’une base de données de vulnérabilités suffisamment large pour une bonne généralisation, et certains aspects de l’entraînement sont non déterministes, comme l'étiquetage automatique ou le filtrage de notre dataset, ce qui pourrait introduire des variations imprévisibles.

\begin{figure}[H]
    \centering
    \includegraphics[width=0.7\textwidth]{Figures/class-distribution.png}
    \vspace{0.5cm}
    \caption{Distribution déséquilibrée des classes dans le dataset de test.}
    \label{fig:class_imbalance}
\end{figure}

Une autre limitation est illustrée dans les résultats obtenus avec la matrice de confusion et la séparation des classes. Ces visualisations montrent les défis liés à la classification des vulnérabilités, notamment les faux positifs et faux négatifs.

\begin{figure}[H]
    \centering
    \includegraphics[width=0.8\textwidth]{Figures/confusion-matrix.png}
    \vspace{0.5cm}
    \caption{Matrice de confusion des résultats du modèle.}
    \label{fig:confusion_matrix}
\end{figure}

\begin{figure}[H]
    \centering
    \includegraphics[width=0.8\textwidth]{Figures/vulnerable-vs-non.png}
        \vspace{0.5cm}

    \caption{Séparation des classes dans l'espace vectoriel pour l'ensemble de test.}
    \label{fig:class_separation}
\end{figure}

\subsection{Discussion sur l'architecture du modèle}

Le modèle proposé dans l'étude repose sur une architecture relativement simple composée d'une unique couche \texttt{LSTM}, suivie d'une couche dense avec une fonction d'activation sigmoïde. Bien que ce modèle utilise des techniques d'apprentissage profond, il reste limité en profondeur et en capacité de généralisation. En effet, l'absence de couches empilées (\textit{stacked LSTMs}) ou de mécanismes d'attention limite la capacité du réseau à capturer des relations complexes et hiérarchiques dans les données. De plus, l'utilisation d'une seule couche récurrente peut être insuffisante pour modéliser efficacement les dépendances à long terme présentes dans le code source. Une amélioration potentielle de cette architecture pourrait inclure des LSTMs bidirectionnels, des mécanismes d'attention (tels que les Transformers) ou une combinaison de couches convolutionnelles (CNN) pour extraire des caractéristiques locales avant d'utiliser les LSTMs pour les relations séquentielles. Ainsi, bien que l'étude utilise une approche d'apprentissage profond, la simplicité de l'architecture actuelle pourrait constituer une limite à ses performances dans des tâches de détection de vulnérabilités plus complexes.

\subsection{Possibilité et pertinence d'une classification multi-classes}

Une extension du modèle VUDENC actuel de classification binaire vers une classification multi-classes est une piste intéressante à explorer. En effet, une approche multi-classes permettrait non seulement de détecter la présence d'une vulnérabilité dans le code, mais également d'identifier le type spécifique de vulnérabilité (par exemple, injection SQL, XSS, débordement de tampon, etc.). Cela offrirait une vision plus détaillée des failles potentielles et permettrait d'observer des combinaisons ou des mélanges de vulnérabilités, un phénomène courant dans des contextes réels où plusieurs types de risques peuvent coexister au sein d'un même extrait de code.

L'article \textit{µVulDeePecker \cite{DBLP:journals/corr/abs-2001-02334}} démontre qu'une telle approche est réalisable, mais elle nécessite des ajustements considérables au niveau de l'architecture du modèle. Contrairement aux modèles classiques de classification binaire, comme \textit{VUDENC}, qui utilisent des réseaux LSTM simples, \textit{µVulDeePecker} introduit des concepts avancés pour améliorer la classification multi-classes :
\begin{itemize}
    \item Le \textit{code attention}, qui identifie les portions spécifiques du code essentielles pour distinguer les types de vulnérabilités.
    \item Une version améliorée des gadgets de code, prenant en compte à la fois les relations de dépendance de données et de dépendance de contrôle pour extraire des informations globales et locales.
    \item Une architecture neuronale complexe basée sur la fusion des caractéristiques, combinant des informations globales (relations entre instructions) et locales (détails précis des instructions).
\end{itemize}

\begin{figure}[h!]
    \centering
    \includegraphics[width=0.8\textwidth]{Figures/vudenpec.png}
    \vspace{0.2cm}
    \caption{L'architecture neuronale de μVulDeePecker. 
    \cite{DBLP:journals/corr/abs-2001-02334}}
    \label{fig:multi-classes-model}
\end{figure}

Cette sophistication architecturale montre que la classification multi-classes est techniquement possible, mais qu'elle implique une augmentation significative de la complexité. Cela nécessite une préparation rigoureuse des données (comme l'extraction et la normalisation des gadgets de code) ainsi qu'une adaptation des métriques d'évaluation pour les tâches multi-classes. L'expérience menée avec \textit{µVulDeePecker} prouve également que l'intégration de mécanismes tels que la dépendance de contrôle améliore considérablement les performances de détection.

En conclusion, bien que nous n'ayons pas eu l'opportunité d'expérimenter cette approche dans le cadre de ce travail, la classification multi-classes représente une perspective prometteuse pour des recherches futures. Son adoption permettrait non seulement d'affiner la détection des vulnérabilités, mais également d'enrichir l'analyse des failles complexes présentes dans les logiciels.

\subsection{Conclusion}

Ce mémoire a démontré le potentiel de l’apprentissage profond pour la détection de vulnérabilités dans le code Python, tout en mettant en lumière des limitations liées aux datasets et à l’architecture. Les perspectives futures incluent l’amélioration des données et l’intégration de mécanismes avancés afin de renforcer la précision et l’applicabilité des modèles dans des contextes réels.





