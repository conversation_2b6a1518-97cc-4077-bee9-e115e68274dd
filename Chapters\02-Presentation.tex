\titlespacing*{\chapter}{0pt}{-10pt}{40pt}
\chapter{Presentation}
\label{cp:presentation}
% VUDENC (Vulnerability Detection with Deep Learning on a Natural Codebase) est un outil de détection de vulnérabilités basé sur l'apprentissage profond, conçu pour dépasser les limitations des méthodes existantes. Il traite le code source comme une séquence de tokens—identificateurs, mots-clés, littéraux, opérateurs, etc.—ce qui permet une analyse précise et détaillée.
% \par
% \noindent En utilisant word2vec, ces tokens sont transformés en vecteurs numériques qui capturent les similarités sémantiques entre eux, représentant ainsi le code dans un espace vectoriel adapté à l'apprentissage automatique.\\

% \noindent Pour la classification, VUDENC s'appuie sur un réseau de neurones à mémoire à long et court terme (LSTM), capable d'identifier les vulnérabilités à un niveau granulaire fin en analysant les séquences de tokens. 
% \par
% \noindent Les données d'entraînement sont étiquetées automatiquement en extrayant les corrections de vulnérabilités à partir de l'historique des commits de projets open-source, éliminant ainsi le besoin d'intervention manuelle.
% \par
% \noindent De plus, VUDENC est conçu pour être applicable à une large gamme de logiciels sans dépendance spécifique à un projet ou à une configuration particulière, ce qui lui confère une grande polyvalence.

\section{Architecture du modèle VUDENC}

L’approche de VUDENC se décompose en trois étapes principales, chacune répondant à une problématique spécifique de la détection de vulnérabilités.

    \begin{figure}[h]
        \centering
        \includegraphics[width=1\textwidth]{Figures/Architecture.png}
        \caption{Architecture du modèle VUDENC. \cite{DBLP:journals/corr/abs-2201-08441}}
    \end{figure}





\subsection{Étiquetage automatique des données} 
VUDENC utilise un système d'étiquetage automatisé basé sur l’analyse des commits correctifs dans l’historique de projets open-source. Les zones du code modifiées pour corriger des vulnérabilités sont identifiées et utilisées comme exemples positifs, permettant de construire un jeu de données conséquent et pertinent. Cette méthode vise à renforcer la reproductibilité et à améliorer la capacité du modèle à s’adapter à différents contextes et projets.

\subsection{Représentation sémantique du code} 
Le code source est segmenté en une séquence de tokens (identificateurs, mots-clés, littéraux, opérateurs, etc.), qui sont ensuite convertis en vecteurs numériques à l’aide de Word2Vec. Cette méthode d’embedding place les tokens dans un espace vectoriel où la proximité entre vecteurs reflète leur similarité sémantique ou syntaxique. Cela permet de représenter les relations entre les éléments du code sous une forme adaptée aux analyses ultérieures.

\subsection{Analyse séquentielle via LSTM} 
La suite de vecteurs obtenue est traitée par un réseau de neurones LSTM, une architecture capable de prendre en compte les dépendances à longue portée au sein d’une séquence. Cela permet d’examiner des indices révélateurs de vulnérabilités même lorsqu’ils sont répartis à différents endroits dans le code, en exploitant la capacité du LSTM à mémoriser des informations contextuelles pour améliorer l’analyse.\\


Ces trois étapes — étiquetage automatique, classification séquentielle et représentation vectorielle — forment la base du fonctionnement de VUDENC. Ce modèle est conçu pour analyser de larges bases de code, extraire des informations sémantiques et identifier des vulnérabilités variées, tout en minimisant la nécessité d'une intervention humaine.

\section{Étiquetage automatique des données} \label{etiquetage}

L’étiquetage automatique des données constitue un aspect central de la méthode employée, visant à éviter la dépendance à une annotation humaine. Au lieu de recourir à des experts pour marquer manuellement chaque fragment de code comme vulnérable ou non, l’approche consiste à exploiter l’historique des projets open-source pour repérer les corrections de failles de sécurité.



\subsection{Principe de l’étiquetage automatique}

Le processus d’étiquetage automatique part du constat que les modifications apportées au code pour corriger une vulnérabilité constituent, en elles-mêmes, une forme d’annotation implicite. Lorsqu’un développeur commite des changements pour remédier à une faille, il laisse une trace exploitable par un outil d’analyse \cite{10.1145/3236024.3264598}. En identifiant ces commits correctifs, on peut ainsi isoler des exemples d’artefacts de code avant/après correction, permettant de distinguer les portions vulnérables de celles remaniées en réponse à un problème de sécurité.

\begin{figure}[h]
    \centering
    \includegraphics[width=1\textwidth, height=0.2\textheight]{Figures/FocusBlocks.png}
    \caption{Processus de division du code complet en extraits avec des parties vulnérables (rouge) et non vulnérables (vert) pour l'ensemble de données. \cite{DBLP:journals/corr/abs-2201-08441}}
    \label{fig:focusblocks}
\end{figure}


\subsection{Étapes de l’étiquetage automatique}

La procédure d’étiquetage se déroule en plusieurs étapes :

\begin{enumerate} \item \textbf{Extraction de l’historique des commits :} Le dépôt de code source est parcouru afin de lister l’ensemble des révisions, incluant les messages de commit, la date, l’auteur, et bien sûr, les modifications apportées au code.

\item \textbf{Filtrage des commits liés à des vulnérabilités :} Il est nécessaire d’identifier les changements explicitement destinés à corriger une faille de sécurité. Ce repérage s’effectue généralement à l’aide de mots-clés dans les messages de commit (e.g., « fix vuln », « security patch », etc.) ou en croisant différentes sources (références à un CVE, tickets associés).

\item \textbf{Localisation des fragments modifiés :} Une fois les commits ciblés identifiés, l’analyse s’intéresse aux portions de code modifiées. Les lignes ajoutées, supprimées ou modifiées sont examinées. L’hypothèse est que le code affecté par ces modifications correspond au cœur de la vulnérabilité corrigée.

\item \textbf{Création d’exemples vulnérables/non vulnérables :} Les fragments de code avant la correction peuvent être considérés comme des exemples positifs (i.e., vulnérables), tandis que les versions corrigées servent de références pour des échantillons non vulnérables ou, du moins, moins risqués. Cette approche comparative permet de générer automatiquement un ensemble d’exemples étiquetés.

\end{enumerate}

\subsection{Limitations et considérations}

Bien que l’étiquetage automatique soit attrayant d’un point de vue pratique, certaines limites et points d’attention doivent être soulignés :

\begin{itemize} \item \textbf{Qualité des labels :} Les messages de commit et les heuristiques utilisées pour filtrer les vulnérabilités ne garantissent pas une annotation parfaitement fiable. Certains commits de maintenance ou de refactorisation pourraient être confondus avec de véritables corrections de vulnérabilités.

\item \textbf{Granularité de l’étiquetage :} L’association entre le commit correctif et la vulnérabilité peut manquer de précision. Il se peut que l’ensemble des modifications apportées au moment d’un patch ne soient pas toutes liées à la vulnérabilité, entraînant un bruit supplémentaire dans les données.

\item \textbf{Représentativité :} Les vulnérabilités corrigées dans un projet open-source donné ne reflètent pas nécessairement la diversité de failles existant dans d’autres contextes. Le choix du corpus initial affecte donc la capacité du modèle à généraliser.


\end{itemize}

\section{Word2Vec}

Word2Vec, introduit par \cite{mikolov2013efficient}, est une méthode d’apprentissage non supervisée permettant de représenter des mots (ou dans notre cas, des tokens de code) sous forme de vecteurs. L’idée centrale est qu’un mot doit être décrit par son contexte. Pour cela, Word2Vec apprend à prédire soit le mot à partir de son contexte (Continuous Bag-of-Words, CBOW), soit le contexte à partir du mot (Skip-gram). Cette approche permet d’obtenir des vecteurs où la similarité cosinus entre vecteurs reflète la proximité fonctionnelle ou sémantique des éléments.

\subsection{Les deux variantes principales de Word2Vec}

Word2Vec propose deux modèles d’entraînement principaux :

\begin{enumerate} \item \textbf{CBOW (Continuous Bag-of-Words) :} Ce modèle prédit le mot cible à partir de ses voisins. Par exemple, donné le contexte {« if », « variable », « == »}, CBOW essaie de deviner le mot manquant au centre. Pour le code, cela se traduit par la prédiction d’un token à partir de ceux qui l’entourent, capturant les régularités structurelles et contextuelles.

\item \textbf{Skip-gram :} Inversement, le modèle Skip-gram prend un token central et tente de prédire les mots du contexte. Cette approche est souvent jugée plus performante pour les corpus peu fréquents ou hétérogènes, puisqu’elle apprend à partir de chaque occurrence ciblée. Dans le contexte du code, le Skip-gram peut aider à mieux représenter des tokens rares en exploitant au maximum leur contexte.

\end{enumerate}

    \begin{figure}[h]
        \centering
        \includegraphics[width=1\textwidth, height=0.35\textheight]{Figures/word2vec2.png}
        \caption{Architectures de modèles. L'architecture CBOW prédit le mot actuel en fonction du contexte, tandis que le Skip-gram prédit les mots environnants à partir du mot actuel. \cite{mikolov2013efficient}}
    \end{figure}

Les deux modèles ont des caractéristiques similaires en termes d’objectif (apprendre des embeddings), mais CBOW est souvent plus rapide et stable, tandis que Skip-gram a un léger avantage en termes de qualité des représentations pour des mots rares ou des corpus moins réguliers.

\subsection{Word2Vec dans VUDENC}

L’article présentant VUDENC utilise Word2Vec pour traduire les tokens de code en vecteurs. Le choix de Word2Vec, bien que non explicitement comparé à d’autres méthodes dans ce travail, s’explique par plusieurs raisons :

\begin{itemize} \item \textbf{Adaptation facile au code source :} Word2Vec est agnostique au langage et à la nature des tokens. Il n’a pas été conçu spécifiquement pour la langue naturelle ou le code, mais son principe de base — apprendre une représentation distribuée des tokens à partir de leur contexte — est suffisamment générique pour s’appliquer au code source, qui peut être vu comme un « langage » avec une grammaire et des régularités.

\item \textbf{Efficacité et simplicité :} Word2Vec est un modèle relativement léger et rapide à entraîner, même sur des données massives, contrairement à certaines approches plus complexes (par exemple, des embeddings contextuels issus de Transformers). Dans un cadre industriel ou de recherche appliquée, où l’on dispose d’un grand volume de code, la rapidité de l’entraînement et la simplicité de l’implémentation sont des arguments en faveur de Word2Vec.

\item \textbf{Large adoption dans la recherche :} À l’époque de la conception de VUDENC, Word2Vec était déjà un standard dans le domaine des embeddings de mots. Cette notoriété s’est traduite par une disponibilité d’implémentations stables, une documentation abondante, et des bonnes pratiques clairement établies, réduisant le temps d’expérimentation et de paramétrage.

\item \textbf{Flexibilité quant aux variations linguistiques :} Les tokens de code ne se limitent pas à des mots-clés ou des noms de variables. Le code contient un ensemble de symboles et de structures qui ne sont pas des mots au sens linguistique. Word2Vec, n’étant pas restreint par un vocabulaire ou des entités spécifiquement langagières, peut incorporer ces variations arbitraires de manière fluide.

\end{itemize}

\subsection{Pourquoi Word2Vec et pas une autre méthode ?}

D’autres méthodes auraient pu être envisagées, comme GloVe \cite{inproceedings}, fastText \cite{DBLP:journals/corr/BojanowskiGJM16}, ou même des modèles plus récents basés sur les Transformers (p. ex. BERT \cite{DBLP:journals/corr/abs-1810-04805}). Néanmoins, les raisons suivantes peuvent expliquer le choix de Word2Vec :

\begin{itemize} \item \textbf{Maturité et stabilité :} Au moment de la conception de VUDENC, Word2Vec était largement éprouvé. Les méthodes plus récentes, telles que les modèles BERT-like, sont plus complexes et plus coûteuses en temps de calcul, ce qui peut poser des difficultés opérationnelles.

\item \textbf{Coût computationnel :} Les approches plus avancées et dynamiques (comme BERT) requièrent des ressources de calcul plus importantes, tant pour l’entraînement que pour l’inférence. Dans un contexte où le dataset est volumineux et où le temps de développement est contraint, Word2Vec reste une solution plus économique.

\item \textbf{Complexité :} Les modèles plus récents impliquent souvent une complexité architecturale et conceptuelle plus élevée. La simplicité de Word2Vec, alliée à sa capacité à fournir des représentations utiles, a pu être considérée comme suffisante dans l’optique du projet, surtout s’il s’agit de démontrer une faisabilité ou d’amorcer une ligne de recherche.

\item \textbf{Alignement avec les objectifs initiaux :} L’approche décrite par VUDENC se focalise avant tout sur la détection de vulnérabilités via une analyse séquentielle et un étiquetage automatique, et non sur l’innovation dans la représentation des tokens. Dans ce contexte, Word2Vec, déjà validé par d’autres travaux, constitue un choix pragmatique, répondant à la nécessité de produire rapidement des représentations vectorielles de qualité suffisante.

\end{itemize}

\section{LSTM}

Les LSTM (Long Short-Term Memory), introduits \cite{hochreiter1997lstm}, sont une variante des réseaux de neurones récurrents (RNN) conçue pour remédier au problème du gradient qui disparaît ou explose lors de l’apprentissage sur de longues séquences. Une cellule LSTM intègre plusieurs portes (d’entrée, de sortie et d’oubli) permettant de contrôler le flux d’informations : une partie de l’information est conservée plus longtemps, tandis que d’autres portions sont oubliées si elles ne sont plus pertinentes. Cette architecture permet aux LSTM de capturer des dépendances à longue portée, un aspect essentiel pour le traitement de séquences complexes, telles que des phrases en langage naturel ou des fragments de code dont la signification peut dépendre de tokens situés loin dans la séquence.

    \begin{figure}[h]
        \centering
        \includegraphics[width=1\textwidth, height=0.35\textheight]{Figures/lstm_cellule.png}
        \caption{Le détail de la cellule LSTM. \cite{géron2019hands}}
    \end{figure}

Concrètement, un LSTM reçoit, à chaque pas temporel, un vecteur d’entrée (le token actuel représenté par un embedding) ainsi que son propre état interne mis à jour, composé d’un état caché et d’un état mémoire. Les portes internes modulant l’information déterminent quelles informations conserver, quelles informations ajouter, et quelles informations oublier. De cette manière, le LSTM est apte à retenir des données utiles sur une grande distance dans la séquence, contrairement aux RNN standards, plus limités dans leur capacité de mémorisation.

\subsection{Pourquoi un LSTM dans VUDENC ?}

Dans le contexte de VUDENC, le choix d’un LSTM pour la classification séquentielle du code repose sur plusieurs considérations :

\begin{itemize} \item \textbf{Capturer des dépendances à longue portée :} Le code source contient fréquemment des relations entre tokens géographiquement éloignés. Par exemple, la vulnérabilité peut résulter de l’utilisation inappropriée d’une variable définie plus haut dans la fonction, ou d’une dépendance logique complexe répartie sur plusieurs lignes. Les LSTM, grâce à leur mémoire interne, sont mieux armés que des RNN simples pour modéliser ces relations à longue distance.

\item \textbf{Robustesse face à la longueur des séquences :} Les fragments de code analysés peuvent varier en longueur, et certains constructeurs, fonctions ou blocs conditionnels peuvent s’étendre sur de nombreuses lignes. Un LSTM permet de traiter ces séquences variables sans perdre la capacité à retenir les informations pertinentes. D’autres approches séquentielles plus simples (comme des modèles de Markov ou des RNN classiques) rencontrent rapidement des problèmes lorsque la taille de la séquence augmente.

\item \textbf{Expérience et maturité de l’architecture :} Avant la montée en puissance des Transformers, les LSTM étaient une référence établie pour les tâches séquentielles complexes, qu’il s’agisse de traitement du langage naturel, de reconnaissance de la parole ou, dans ce cas, d’analyse de code. L’architecture LSTM est bien documentée, largement implémentée dans les principales bibliothèques de deep learning, et considérée comme un standard dès lors qu’il s’agit d’aborder des dépendances temporelles ou structurelles non triviales.

\item \textbf{Simplicité relative par rapport à des modèles plus récents :} Bien que des architectures plus récentes, comme les Transformers, offrent potentiellement de meilleures performances, elles sont également plus exigeantes en termes de ressources, plus difficiles à mettre en place, et nécessitent généralement davantage de données. Dans le contexte de VUDENC, un LSTM présente un compromis raisonnable : assez puissant pour saisir les dépendances utiles, mais moins complexe à entraîner et à déployer qu’un modèle à attention complexe \cite{DBLP:journals/corr/VaswaniSPUJGKP17}.

\item \textbf{Alignement avec la méthode d’étiquetage et la représentation des données :} L’idée centrale de VUDENC est de traiter le code comme une séquence de tokens. Les LSTM s’intègrent naturellement dans cette approche, puisque leur structure est pensée pour l’exploitation de données séquentielles. Combinés aux embeddings Word2Vec, les LSTM forment une pipeline logique où les tokens encodés de manière distribuée sont ensuite passés dans un réseau récurrent capable de détecter des patterns de vulnérabilités dans le temps (ou le long de la séquence de code).

\end{itemize}

En somme, le choix du LSTM dans VUDENC se justifie par sa capacité à gérer des dépendances à longue portée, à traiter des séquences variables et potentiellement longues, ainsi que par sa maturité au moment de la conception de l’outil. L’objectif n’étant pas d’innover sur l’architecture de réseau de neurones mais d’appliquer une solution robuste et éprouvée, les LSTM apparaissent comme une option raisonnable et cohérente pour le problème de la détection de vulnérabilités dans le code.

