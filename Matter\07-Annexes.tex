\ifthenelse{\equal{\getLanguage}{portuguese}}{%
    \addtocontents{toc}{\protect\contentsline{chapter}{Anexos}{}{}}
}{%
    \addtocontents{toc}{\protect\contentsline{chapter}{Annexes}{}{}}
}

\setcounter{chapter}{12} % To start at the "M" chapter
\blankpage\newgeometry{margin=4cm}
\begin{center}
    \scshape % Font style
    \thispagestyle{empty}
        
    \vspace*{\fill}
    \ifthenelse{\equal{\getLanguage}{portuguese}}{%
        {\LARGE\fontsize{20}{26}\selectfont\textcolor{darkred}{Anexos}\par}
    }{%
        {\LARGE\fontsize{20}{26}\selectfont\textcolor{darkred}{Annexes}\par}
    }
    \vspace*{\fill}
\end{center}
\restoregeometry\blankpage