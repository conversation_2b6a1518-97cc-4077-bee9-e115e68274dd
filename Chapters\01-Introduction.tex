\titlespacing*{\chapter}{0pt}{-10pt}{40pt}
\chapter{Introduction} \label{cp:introduction}

La sécurité logicielle est un enjeu majeur pour la protection des systèmes informatiques face à des cyberattaques susceptibles de provoquer des pertes d’information, des fuites de données sensibles ou des indisponibilités de service. Les vulnérabilités, souvent issues d’erreurs subtiles dans le code, exposent directement les logiciels à ces risques. Selon la terminologie du CERT/CC, citée par \cite{arbaugh2000windows},
\begin{quote} \textit{« A vulnerability is a technological flaw in an information technology product that has security or survivability implications. »} \end{quote}

Autrement dit, une vulnérabilité est un défaut technologique dans un produit informatique qui impacte sa sécurité ou sa capacité à survivre à des attaques. L’exemple de la faille Heartbleed \cite{10.1145/2663716.2663755} dans la bibliothèque OpenSSL — qui aurait pu être évitée avec deux lignes de code supplémentaires — illustre concrètement comment de petites erreurs peuvent conduire à des répercussions massives.

Face à la complexité croissante des logiciels, la détection manuelle des vulnérabilités est de moins en moins envisageable. C’est pourquoi les techniques automatisées sont devenues essentielles. Ces approches doivent répondre à plusieurs exigences :

\begin{itemize} \item \textbf{Précision élevée :} Identifier les vulnérabilités avec un minimum de faux positifs et de faux négatifs. \item \textbf{Guidage précis :} Indiquer clairement aux développeurs les fragments de code vulnérables. \item \textbf{Évolutivité :} S’adapter à de larges bases de code provenant de projets réels. \item \textbf{Généralisation :} Dépasser le cadre d’un seul projet pour s’appliquer à divers contextes et langages. \item \textbf{Faible effort de configuration :} Limiter le travail manuel requis pour l’adaptation de l’approche. \end{itemize}

De nombreuses approches existent, mais rares sont celles qui satisfont pleinement à ces exigences. Parmi les approches fondées sur l’apprentissage automatique, un récent benchmark \cite{10.1145/3699711} a passé en revue une large gamme de méthodes, allant des algorithmes classiques (régression logistique, forêts aléatoires) aux modèles profonds plus avancés (LSTM, Transformers). Ce travail comparatif a permis de dresser un état des lieux complet des performances, de la complexité et de la capacité de généralisation de ces outils, incluant l’approche étudiée dans ce mémoire. Les résultats présentés dans ce benchmark mettent en évidence une tendance claire : l’utilisation de modèles apprenants pour la détection de vulnérabilités gagne en maturité et se rapproche progressivement d’une application sur des projets réels. 

\begin{figure}[h]
    \centering
    \includegraphics[width=1\textwidth, height=0.30\textheight]{Figures/tendances.png}
    \caption{Tendance des publications d'articles pour la détection des vulnérabilités logicielles. \cite{10.1145/3699711}}
\end{figure}



Toutefois, ils soulignent également la nécessité d’améliorer la qualité des jeux de données, de mieux comprendre la sensibilité à la variabilité des contextes applicatifs, ainsi que d’affiner les techniques d’interprétabilité des modèles. En somme, cette évolution s’accompagne de défis méthodologiques importants, qui doivent être pris en compte pour que ces solutions s’imposent comme des alternatives robustes aux méthodes traditionnelles.

% L’outil VUDENC, par exemple, propose une méthodologie de détection des vulnérabilités à l’aide d’un modèle basé sur l’apprentissage profond. Cependant, une analyse critique approfondie est nécessaire, notamment pour :

% \textbf{Mieux décrire le dataset :} Clarifier son origine, sa structure, le processus d’étiquetage et les caractéristiques des exemples utilisés (voir Chapitre~\ref{cp:dataset}).
% \textbf{Détailler l’architecture du réseau de neurones :} Préciser le type de modèle (LSTM, CNN, etc.), la profondeur, le nombre de neurones par couche, les hyperparamètres et le processus d’entraînement (Chapitre~\ref{cp:model}).
% \textbf{Expliquer l’utilisation de Word2Vec :} Justifier les choix de paramètres et décrire en détail l’impact de cette représentation (Chapitre~\ref{cp:dataset} ou~\ref{cp:model}).
% \textbf{Comparer soigneusement les résultats :} Confronter les résultats obtenus à ceux de l’article original, en tenant compte du contexte expérimental, afin d’évaluer la robustesse et la généralisabilité de l’approche (Chapitre~\ref{cp:results}).
% \textbf{Aborder le non-déterminisme et l’évolutivité vers la classification multi-classes :} Identifier l’origine des variations non déterministes (initialisation, ordre des mini-batches, opérations GPU) et envisager une classification plus fine, multi-classes, pour mieux distinguer différents types de vulnérabilités (Chapitre~\ref{cp:discussion}).
% Cette démarche vise à éviter une simple mise en avant des performances et à souligner plutôt les limites, les conditions de reproductibilité, ainsi que les pistes d’amélioration, afin de fournir une perspective réaliste de l’outil et de sa robustesse réelle en détection de vulnérabilités.