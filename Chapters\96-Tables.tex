\chapter{Tables}
Tables play a vital role in presenting your findings effectively. In this chapter, we delve into various techniques for conveying information through tables, employing different environments available in this template. Although defining tables in \LaTeX\ may appear complex, using this template makes the process more straightforward.

\begin{importantbox}
    Prior to showcasing the different table environments, it's crucial to note that each one must be enclosed within a \verb|\begin{table}| environment. Additionally, it is recommended to utilise the \verb|[!htpb]| float options for improved document placement. \textbf{This advice should be taken into consideration when positioning figures as well}.
\end{importantbox}

\section{Tabular Environment}
The conventional \verb|\begin{tabular}| environment enables you to create a simple yet elegant table. \autoref{tab:table-01} is generated using a centering environment for added emphasis. It also incorporates the \verb|booktab| configuration for a more sophisticated table style.

\begin{table}[!htpb]
    \caption{A table showcasing the usage of the tabular environment.}
    \label{tab:table-01}
    \centering
    \begin{tabular}{llc}
        \toprule
        \textbf{Header 01} & \textbf{Header 02} & \textbf{Header 03} \\ 
        \midrule
        Lorem Ipsum & Lorem Ipsum & $\checkmark$ \\
        Lorem Ipsum & Lorem Ipsum & $\checkmark$ \\
        Lorem Ipsum & Lorem Ipsum & - \\
        Lorem Ipsum & Lorem Ipsum & - \\
        Lorem Ipsum & Lorem Ipsum & $\checkmark$ \\
        \bottomrule
    \end{tabular}
\end{table}

\section{Tabularx Environment}
Employ the \verb|\begin{tabularx}| package to construct a table featuring automatically expanding multi-columns. To achieve this automatic behaviour for multi-columns, utilise the following environment: \verb|\begin{tabularx}{\textwidth}{@{}lX@{}}|. Take note that we substitute \verb|X| in place of \verb|l| or \verb|c|, explicitly indicating that the column will function as a multi-column, occupying the entire available space. \autoref{tab:table-02} showcases the usage of the \verb|begin{tabularx}| environment.

\begin{table}[!htpb]
    \caption{A table showcasing the usage of the tabularx environment.}
    \label{tab:table-02}
    \begin{tabularx}{\textwidth}{lX}
        \toprule
        \textbf{Header 01} & \textbf{Header 02} \\ 
        \midrule
        Foo Bar Baz & Quisque cursus, metus vitae pharetra auctor, sem massa mattis sem, at interdum magna augue eget diam. \\
        Ipsum Dolor & Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Curabitur aliquet quam id dui. \\
        Dolor Sit & Phasellus condimentum elementum justo, quis interdum est sagittis ac. Vestibulum non arcu sit amet justo lobortis semper. \\
        Amet Consectetuer & Integer nec odio praesent libero sed cursus ante dapibus diam sed nisi vestibulum non arcu. \\
        Consectetuer Adipiscing & Nulla quis sem at nibh elementum imperdiet. Duis sagittis ipsum. Praesent mauris. \\
        \bottomrule
    \end{tabularx}
\end{table}

\section{Longtable Environment}
At times, when dealing with exceptionally lengthy tables, it becomes necessary to split them across multiple pages. In \LaTeX, this can be achieved using the \verb|\begin{longtable}| environment. Feel free to consult \autoref{tab:table-03} for a detailed demonstration of how the \verb|longtable| operates.

\begin{longtable}[c]{llll}
\caption{A table showcasing the usage of the longtable environment.}
\label{tab:table-03} \\
\toprule
\textbf{Names} & \textbf{E-Mails} & \textbf{Job/Role} \\ \midrule
\endfirsthead
%
\multicolumn{4}{c}%
{{\bfseries Table \thetable\ continued from previous page}} \\
\toprule
\textbf{Names} & \textbf{E-Mails} & \textbf{Job/Role} \\ \midrule
\endhead
%
\bottomrule
\endfoot
%
\endlastfoot
%
Alice Johnson & <EMAIL> & Project Manager \\
Bob Thompson & <EMAIL> & Data Analyst \\
Charlie Davis & <EMAIL> & Marketing Specialist \\
David Miller & <EMAIL> & QA Tester \\
Emily White & <EMAIL> & Graphic Designer \\
Frank Martin & <EMAIL> & HR Coordinator \\
Grace Turner & <EMAIL> & Financial Analyst \\
Henry Lee & <EMAIL> & System Administrator \\
Ivy Carter & <EMAIL> & Customer Support \\
Jack Wilson & <EMAIL> & Frontend Developer \\
Jane Reed & <EMAIL> & UX Designer \\
Kevin Evans & <EMAIL> & Product Manager \\
Linda Adams & <EMAIL> & Accountant \\
Mike Hill & <EMAIL> & Network Engineer \\
Nina Garcia & <EMAIL> & Business Analyst \\
Oliver Smith & <EMAIL> & Sales Representative \\
Pamela Turner & <EMAIL> & Legal Counsel \\
Quincy Brown & <EMAIL> & IT Consultant \\
Rachel Moore & <EMAIL> & Content Writer \\
Samuel White & <EMAIL> & Research Scientist \\ \bottomrule
\end{longtable}

\section{Complex Tables}
Creating intricate tables in \LaTeX\ can be a somewhat challenging task. Therefore, we highly recommend using the \href{https://www.tablesgenerator.com/}{Table Generator}. With this tool, you can design your table with the desired style and then easily copy and paste it into your document. This approach simplifies the process and helps ensure the accurate representation of complex tables in your \LaTeX\ document. However, it's crucial to keep in mind that a table should be easily comprehensible for the reader and should not be overly complex. The complexity of a table may impede understanding. For example, \autoref{tab:table-04} presents a table with intricate details.

\begin{table}[!htpb]
    \caption{A table showcasing the usage of the complex tables.}
    \label{tab:table-04}
    \centering
    \begin{tabular}{lcc}
        \toprule
        \multirow{2}{*}{\textbf{Category}} & \multicolumn{2}{c}{\textbf{Details}} \\
        \cmidrule(lr){2-3}
        & \textbf{Subcategory} & \textbf{Carried Out} \\
        \midrule
        \multirow{3}{*}{Long Category Name A} & Long Subcategory Name A & $\checkmark$ \\
        & Ipsum & $\checkmark$ \\
        & Adipiscing & - \\
        \midrule
        \multirow{3}{*}{Long Category Name B} & Long Subcategory Name B & - \\
        & Ipsum & - \\
        & Adipiscing & - \\
        \midrule
        \multirow{3}{*}{Long Category Name C} & Long Subcategory Name C & $\checkmark$ \\
        & Consectetur & $\checkmark$ \\
        & Adipiscing & - \\
        \bottomrule
    \end{tabular}
\end{table}