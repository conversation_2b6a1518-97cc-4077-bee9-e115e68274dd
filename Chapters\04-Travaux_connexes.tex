\titlespacing*{\chapter}{0pt}{-10pt}{40pt}
\chapter{Travaux connexes}
\label{cp:travaux connexes}
Dans le domaine de la détection des vulnérabilités, les approches traditionnelles se répartissent généralement en trois catégories principales : les méthodes basées sur les métriques logicielles, celles fondées sur la détection d'anomalies et celles axées sur l'analyse des mauvaises utilisations (misuse detection). 
\par
\noindent Les techniques basées sur les métriques logicielles utilisent des indicateurs tels que le nombre de lignes de code ou la complexité cyclomatique pour prédire les vulnérabilités. Cependant, elles manquent souvent de précision car elles ne capturent pas la sémantique du code et se généralisent mal entre différents projets. 
\par
\noindent Les approches de détection d'anomalies cherchent à identifier les écarts par rapport aux modèles de code normaux, en supposant que les anomalies correspondent à des vulnérabilités. Cette supposition conduit fréquemment à un taux élevé de faux positifs, reposant sur l'hypothèse erronée que le code typique est toujours sécurisé. 
\par
\noindent Enfin, les méthodes d'analyse des mauvaises utilisations tentent d'identifier des motifs caractéristiques de code vulnérable en se basant sur des fragments connus. Néanmoins, elles opèrent souvent à une granularité trop grossière, dépendent de données synthétiques ou de projets spécifiques, et manquent de précision dans la localisation des vulnérabilités.
\par
\noindent Face à ces limitations, VUDENC se distingue en apportant plusieurs innovations majeures. Travaillant directement sur le code source textuel, il permet une analyse fine sans se limiter aux indicateurs superficiels des métriques logicielles. 
\par
\noindent En intégrant des techniques avancées d'apprentissage profond telles que word2vec et les réseaux de neurones LSTM, VUDENC capture la sémantique profonde du code, améliorant ainsi la précision de la détection des vulnérabilités. 
\par
\noindent Contrairement aux méthodes opérant à une granularité élevée, VUDENC offre une détection à un niveau granulaire fin, pointant directement les fragments de code vulnérables. Cette précision facilite grandement le travail des développeurs pour corriger les failles identifiées. 
\par
\noindent De plus, grâce à son entraînement sur un vaste ensemble de codes issus de projets réels, il est applicable à une large gamme de logiciels sans dépendance à des données synthétiques ou spécifiques à un projet particulier, ce qui le rend polyvalent et efficace dans divers contextes.