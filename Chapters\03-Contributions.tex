\titlespacing*{\chapter}{0pt}{-10pt}{40pt}
\chapter{Données, Prétraitement et Paramétrage}
\label{cp:contributions}
Ce chapitre présente les données utilisées, les opérations de prétraitement mises en œuvre et les paramètres choisis pour l’entraînement du modèle VUDENC. Il vise à clarifier la provenance des échantillons, la manière dont ils ont été étiquetés, la représentation des tokens de code, ainsi que les principaux hyperparamètres adoptés, tant pour les embeddings que pour l’architecture du réseau LSTM. Enfin, la stratégie de validation et les métriques d’évaluation retenues seront également évoquées.

\section{Présentation du Jeu de Données}

Les données proviennent de projets open-source écrits en Python, incluant des fragments de code vulnérables et non vulnérables. La constitution de ce jeu de données a déjà été présentée dans une section antérieure (voir la section~\ref{etiquetage}). 

\begin{table}[h!]
    \centering
    \begin{tabular}{|l|r|r|r|r|r|r|}
        \hline
        \textbf{Vulnerability type} & \textbf{\# repo} & \textbf{\# commits} & \textbf{\# changed files} & \textbf{LOC} & \textbf{\#functions} & \textbf{\# chars} \\ \hline
        SQL injection          & 336 & 406 & 657 & 83,558 & 5,388 & 3,960,074 \\ \hline
        XSS                 & 39  & 69  & 81  & 14,916 & 783   & 736,567   \\ \hline
        Command injection     & 85  & 106 & 197 & 36,031 & 2,161 & 1,740,339 \\ \hline
        XSRF                   & 88  & 141 & 296 & 56,198 & 4,418 & 2,682,206 \\ \hline
        Remote code execution & 50  & 54  & 131 & 30,591 & 2,592 & 1,455,087 \\ \hline
        Path disclosure        & 133 & 140 & 232 & 42,303 & 2,968 & 2,014,413 \\ \hline
        Open redirect         & 81  & 93  & 182 & 26,521 & 1,762 & 1,295,748 \\ \hline
    \end{tabular}
    \caption{Statistiques des types de vulnérabilités provenant de projets open-source.\cite{DBLP:journals/corr/abs-2201-08441}}

    \label{tab:vulnerability_statistics}
\end{table}


\subsection{Types de Vulnérabilités Ciblées}

Le jeu de données couvre plusieurs catégories de vulnérabilités couramment rencontrées dans le code Python. Parmi ces types, on retrouve :

\begin{itemize} \item \textit{Command Injection} : Vulnérabilités liées à l’exécution non contrôlée de commandes système. \item \textit{Open Redirect} : Redirections d’URL non sécurisées, permettant à un attaquant de tromper un utilisateur en le dirigeant vers un site malveillant. \item \textit{Path Disclosure} : Vulnérabilités révélant des informations sensibles sur la structure et la localisation des fichiers du système. \item \textit{Remote Code Execution} : Exécution à distance de code arbitraire sur le système cible, souvent la plus critique. \item \textit{SQL Injection} : Injections SQL permettant de manipuler les requêtes envers la base de données, conduisant à des fuites ou des altérations de données. \item \textit{XSRF (Cross-Site Request Forgery)} : Attaques incitant un utilisateur authentifié à réaliser des actions non souhaitées sur une application web. \item \textit{XSS (Cross-Site Scripting)} : Exécution de scripts arbitraires dans le navigateur de la victime, pouvant conduire à du vol de cookies ou à des redirections malveillantes. \end{itemize}

Ces différentes catégories sont représentées dans les fichiers de données (par exemple, \texttt{plain\_sql}, \texttt{plain\_xss},    etc. \cite{zenodo3559841}), chacun associant des fragments vulnérables à leurs variantes corrigées, identifiées via l’historique de commits.

    \inputminted[fontsize=\small]{json}{Code/example.json}
    \vspace{1cm}
    \textit{Morceaux de dataset de vulnérabilités, extrait d'un fichier plain\_\**\..json \cite{zenodo35598412}}

    

\subsection{Nettoyage et Filtrage Initial}

Avant l’extraction des caractéristiques, les données subissent un filtrage destiné à éliminer les échantillons de mauvaise qualité, les fichiers inexploitables ou les exemples contenant des artefacts syntaxiques trop importants (problèmes d’indentation, code incomplet). De même, les doublons ou extraits trop longs ou trop courts sont écartés afin de disposer d’un ensemble de données plus cohérent. Ce processus vise à réduire le bruit dans le corpus, de manière à obtenir un échantillon plus pertinent pour l’entraînement du modèle.

\section{Prétraitement des Données}

\subsection{Tokenisation du Code}

La première étape de prétraitement consiste à convertir le code source en une séquence de tokens. Un « token » correspond typiquement à un identificateur, un mot-clé du langage, un opérateur, un littéral ou un symbole structurel. La tokenisation permet de passer du niveau brut (une suite de caractères) à une représentation plus structurée, facilitant l’application ultérieure de modèles d’apprentissage automatique.

\subsection{Étiquetage Automatique des Échantillons}

L’étiquetage n’est pas réalisé manuellement, mais se fonde sur l’analyse de commits correctifs. Lorsque des développeurs corrigent une vulnérabilité, la portion de code modifiée constitue un exemple vulnérable, tandis que la version corrigée est considérée comme non vulnérable. Ce procédé, discuté plus en détail dans la section dédiée à l’étiquetage, permet de générer automatiquement des ensembles de données labellisés sans intervention humaine directe, tout en introduisant néanmoins un certain degré d’incertitude dans la qualité des labels.

\subsection{Normalisation et Mise en Forme des Séquences}

Les séquences de tokens diffèrent en longueur. Pour traiter ce problème, des techniques comme le « padding » (ajout de tokens neutres jusqu’à atteindre une longueur fixe) sont utilisées. L’objectif est de produire un ensemble d’entrées de taille uniforme, nécessaire au traitement par le modèle LSTM.

\section{Sélection des Hyperparamètres}

\subsection{Paramètres de Word2Vec}

Pour représenter les tokens, une méthode Word2Vec est employée. Des paramètres spécifiques sont fixés, tels que :

\begin{itemize} \item \textbf{Dimension des Embeddings :} Une dimension vectorielle de 200 a été retenue. \item \textbf{Filtrage (minCount):} Les tokens apparaissant moins de 10 fois dans le corpus sont ignorés, ce qui permet d’éliminer les termes trop rares et potentiellement non pertinents, renforçant ainsi la qualité globale des représentations. \item \textbf{Nombre d’Itérations :} Le modèle a été entraîné sur 100 itérations, assurant une convergence stable du Word2Vec. \end{itemize}

Ces choix ont un impact direct sur la qualité des embeddings, la stabilité des résultats et le temps de calcul.


\begin{figure}[h!] \centering \includegraphics[width=1\textwidth, height=0.6\textheight]{Figures/Word2Vecparam.png} \caption{Influence des hyperparamètres Word2Vec sur le F1-score obtenu. \cite{DBLP:journals/corr/abs-2201-08441}} \label{fig:word2vec_params} \end{figure}

\subsection{Architecture du Réseau LSTM}

Le cœur du modèle est un LSTM conçu pour exploiter l’information séquentielle présente dans le code :

\begin{itemize} \item \textbf{Profondeur et Largeur du Réseau} : Nombre de couches LSTM empilées et nombre de neurones par couche (100 neurones). \item \textbf{Fonction d’Activation} : La couche de sortie utilise une fonction sigmoïde pour produire une probabilité. \item \textbf{Régularisation} : Utilisation d’un taux de dropout pour limiter le surapprentissage. \end{itemize}

Ces hyperparamètres affectent directement la capacité du réseau à capturer les régularités, sans se suradapter.

\begin{figure}[h!] \centering \includegraphics[width=1\textwidth, height=0.5\textheight]{Figures/lstmparam.png} \caption{Influence des hyperparamètres LSTM sur le F1-score obtenu. \cite{DBLP:journals/corr/abs-2201-08441}} \label{fig:lstmparam} \end{figure}

\section{Choix de l’Optimiseur}

Le choix d’Adam \cite{DBLP:journals/corr/KingmaB14} dans VUDENC reflète une volonté d’adopter un algorithme d’optimisation à la fois simple à mettre en œuvre, efficace en pratique et largement éprouvé dans la communauté de l’apprentissage profond. Adam, grâce à sa gestion adaptative du taux d’apprentissage, facilite l’atteinte d’une performance élevée sans nécessiter un réglage manuel excessif des paramètres. 

\begin{table}[h!]
\centering
\caption{Résultats obtenus avec différents optimiseurs  \cite{DBLP:journals/corr/abs-2201-08441}.}
\begin{tabular}{l c c c c c}
\hline
\textbf{Optimiseur} & \textbf{\# Époques} & \textbf{Accurancy} & \textbf{Precision} & \textbf{Rappel} & \textbf{F1}\\
\hline
Adam    & 10 & 95\% & 85\% & 63\% & 72\% \\
Adagrad & 10 & 94\% & 78\% & 56\% & 65\% \\
Adamax  & 10 & 94\% & 78\% & 56\% & 65\% \\
Nadam   & 10 & 95\% & 86\% & 61\% & 71\% \\
RMSProp & 10 & 95\% & 86\% & 63\% & 73\% \\
SGD     & 10 & 15\% & 10\% & 97\% & 19\% \\
\hline
Adam    & 30 & 96\% & 90\% & 70\% & 79\% \\
RMSProp & 30 & 96\% & 91\% & 70\% & 80\% \\
Nadam   & 30 & 96\% & 90\% & 67\% & 77\% \\
\hline
\end{tabular}
\label{tab:optimizers}
\end{table}





\section{Stratégie de Validation et Métriques d’Évaluation}

\subsection{Séparation Entraînement/Validation/Test}

Le jeu de données est segmenté en ensembles distincts afin de mesurer les performances du modèle sur des données non vues. Dans le cas de VUDENC cette répartition est : 70\% pour l’entraînement, 15\% pour la validation et 15\% pour le test final. 

\subsection{Métriques de Performance}

Les performances sont évaluées à l’aide de métriques telles que Accuracy, Precision, Recall et F1 score. Le F1-score est particulièrement pertinent pour évaluer l’équilibre entre les faux positifs et les faux négatifs, souvent critique dans le domaine de la détection de vulnérabilités.


\[
Precision = \frac{TP}{TP + FP} \tag{1}
\]

\[
Recall = \frac{TP}{TP + FN} \tag{2}
\]

\[
F1 = 2 \cdot \frac{Precision \cdot Recall}{Precision + Recall} \tag{3}
\]

\[
Accuracy = \frac{TP + TN}{TP + FP + TN + FN} \tag{4}
\]
