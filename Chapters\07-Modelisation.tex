\titlespacing*{\chapter}{0pt}{-10pt}{40pt}
\chapter{Modélisation}
\label{cp:modelisation}
La modélisation s'appuie sur un réseau de neurones de type LSTM (Long Short-Term Memory) pour détecter les vulnérabilités au sein du code source. En traitant les séquences de tokens vectorisés, le réseau est entraîné à classifier chaque fragment de code en identifiant non seulement la présence de vulnérabilités, mais aussi en localisant précisément les zones à risque. Les LSTM sont particulièrement adaptés pour capturer les dépendances à long terme dans les données séquentielles, ce qui est crucial pour analyser le code source. De plus, le modèle fournit des niveaux de confiance pour chaque prédiction, aidant les développeurs à prioriser les correctifs en fonction de la probabilité d'une vulnérabilité.\\

\noindent L'architecture LSTM a été choisie pour sa capacité à mémoriser des informations importantes sur de longues séquences de données, tout en ignorant les informations non pertinentes. Cette caractéristique est particulièrement importante dans l'analyse du code source, où les vulnérabilités peuvent dépendre de relations complexes entre différentes parties du code. 
\par
\noindent Le modèle utilise plusieurs couches de neurones LSTM, suivies d'une couche dense finale pour la classification, avec un taux de dropout de 20\% pour prévenir le surapprentissage. Les expérimentations ont montré qu'une configuration avec 100 neurones et 100 époques d'entraînement offrait le meilleur compromis entre performance et temps de calcul.