\ifthenelse{\equal{\getLanguage}{portuguese}}{%
    \pdfbookmark[0]{Capa}{capa} % Add entry to PDF bookmarks group
    \pdfbookmark[1]{Frontispício}{frontispício} % Add entry to PDF
}{%
    \pdfbookmark[0]{Front Matter}{frontmatter} % Add entry to PDF bookmarks group
    \pdfbookmark[1]{Cover}{cover} % Add entry to PDF
}

% Add background picture
\newcommand\BackgroundPicCover{%
    \put(0,0){%
    \parbox[b][\paperheight]{\paperwidth}{%
    \vfill
    \centering
    \includegraphics[width=\paperwidth,height=\paperheight,keepaspectratio]{Figures/Theme/Front-Page-BG.pdf}%
    \vfill
}}}
\AddToShipoutPictureBG*{\BackgroundPicCover}

\newgeometry{margin=1.98cm, top=2.15cm, bottom=1.47cm}
\begin{titlepage}
    \latofont % Switch to Lato font for the title page
    \color{frontpagedark} % Define the color to use within this page
    \vspace*{\baselineskip} % White space at the top of the page

    \begin{figure}
        \includegraphics[width=0.32\linewidth]{Figures/Theme/IPLeiria-Logo-B.pdf}
    \end{figure}

    \vspace{5.3\baselineskip}

    % Title
	\noindent
    \makebox[\textwidth][l]{%
        \parbox{\dimexpr\textwidth-2.5cm\relax}{
            \setstretch{1.03}
            \raggedright\bfseries\fontsize{20}{26}\selectfont\thetitle
        }
    }

    \vspace{75pt}  

    % Author
    {\noindent\bfseries\fontsize{14}{19}\selectfont\firstauthorname}

    \ifdefined\secondauthorname
        \vspace{10pt}
        {\noindent\bfseries\fontsize{14}{19}\selectfont\secondauthorname}
	\fi

    \ifdefined\thirdauthorname
        \vspace{10pt}
        {\noindent\bfseries\fontsize{14}{19}\selectfont\thirdauthorname}
	\fi
 
	\vfill

    % School
	{\noindent\fontsize{10}{12}\selectfont\schoolname}
	
    % Department
	{\noindent\fontsize{10}{12}\selectfont\departmentname}

    % Degree
	{\noindent\fontsize{10}{12}\selectfont\degname}

    % Course
    \ifdefined\coursename
        {\noindent\fontsize{10}{12}\selectfont\coursename}
	\fi

    \vspace{120pt}

    % Local & Date
	{\noindent\fontsize{10}{12}\selectfont\thedate}

    \vspace{68pt}
\end{titlepage}
\restoregeometry\blankpage