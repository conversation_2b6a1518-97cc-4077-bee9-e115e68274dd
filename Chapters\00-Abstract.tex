\thispagestyle{plain}

\chapter*{Abstract}
VUDENC \cite{DBLP:journals/corr/abs-2201-08441} est un outil de détection de vulnérabilités reposant sur l’apprentissage profond, appliqué à des projets Python afin d’identifier automatiquement des fragments de code potentiellement à risque. L’approche utilise des représentations par Word2Vec pour modéliser les tokens et un réseau LSTM pour classifier les séquences, dans le but de cibler divers types de vulnérabilités, telles que les injections SQL ou les failles XSS. Testé sur un ensemble de 1009 commits correctifs issus de plusieurs dépôts GitHub, VUDENC a obtenu, dans l’étude originale, une précision annoncée entre 82\% et 96\% ainsi qu’un rappel entre 78\% et 87\%. Bien que ces performances puissent sembler prometteuses, elles doivent être interprétées avec prudence. La méthodologie, le jeu de données et les conditions expérimentales méritent une analyse critique plus approfondie, afin de déterminer dans quelle mesure ces chiffres reflètent une robustesse réelle de l’outil et sa capacité à s’adapter à d’autres environnements, langages ou contextes de développement.