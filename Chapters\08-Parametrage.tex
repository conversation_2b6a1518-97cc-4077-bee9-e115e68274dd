\titlespacing*{\chapter}{0pt}{-10pt}{40pt}
\chapter{Paramétrage}
\label{cp:parametrage}
Le choix des hyperparamètres pour le réseau LSTM a été effectué à travers une série d'expérimentations rigoureuses basées sur un corpus d'entraînement de 69,517,343 tokens Python. Ces expérimentations ont été éffectués à partir d'un model "baseline", il n'est pas optimal, mais il montre l'influence des hyperparamètres sur les résultats.
\par
\noindent Pour l'embedding word2vec, une dimension vectorielle de 200 a été sélectionnée après des tests variant de 5 à 300 dimensions, ce choix offrant le meilleur compromis pour représenter la sémantique du code Python. Les chaînes de caractères ont été conservées dans leur forme originale plutôt que d'être remplacées par des tokens génériques, car les expérimentations ont montré que cela produisait systématiquement de meilleurs résultats. Le minimum count (nombre minimal d'occurrences d'un token) a été fixé à 10, les tests ayant révélé que l'inclusion d'un maximum de tokens, même rares, améliorait les performances du modèle. Le nombre d'itérations d'entraînement a été fixé à 100, avec un batch size de 128. Le modèle utilise 100 neurones avec un dropout et recurrent dropout de 20\%, et est optimisé avec l'optimiseur Adam.\\

\noindent Cette configuration a été choisie comme le meilleur compromis entre performance et contraintes computationnelles, démontrant des résultats robustes lors des expérimentations empiriques.