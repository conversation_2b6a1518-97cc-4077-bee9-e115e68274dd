\ifthenelse{\equal{\getLanguage}{french}}{%
    \pdfbookmark[1]{Primeira Página}{primeirapagina} % Add entry to PDF
}{%
    \pdfbookmark[1]{Front Page}{frontpage} % Add entry to PDF
}

% Add background picture
\newcommand\BackgroundPicFrontPage{%
    \put(0,0){%
    \parbox[b][\paperheight]{\paperwidth}{%
    \vfill
    \centering
    \includegraphics[width=\paperwidth,height=\paperheight,keepaspectratio]{Figures/Theme/Front-Page-BG.pdf}%
    \vfill
}}}
\AddToShipoutPictureBG*{\BackgroundPicFrontPage}

\newgeometry{margin=1.98cm, top=2.15cm, bottom=1.47cm}
\begin{titlepage}
    \latofont % Switch to Lato font for the title page
    \color{frontpagedark} % Define the color to use within this page
    \vspace*{\baselineskip} % White space at the top of the page

    \begin{figure}
        \includegraphics[width=0.32\linewidth]{Figures/Theme/universite_bordeaux.png}
    \end{figure}

    \vspace{3.5\baselineskip}

    % Title
	\noindent
    \makebox[\textwidth][l]{%
        \parbox{\dimexpr\textwidth-2.5cm\relax}{
            \setstretch{1.03}
            \raggedright\bfseries\fontsize{20}{26}\selectfont\thetitle
        }
    }
    
    \vspace{38pt}

    % Author(s)
    {\noindent\bfseries\fontsize{14}{19}\selectfont\firstauthorname} \\
    {\noindent\itshape\fontsize{10}{12}\selectfont Numéro d'étudiant. \firstauthornum}
    
    \ifdefined\secondauthorname
        \vspace{20pt}
        {\noindent\bfseries\fontsize{14}{19}\selectfont\secondauthorname} \\
        {\noindent\itshape\fontsize{10}{12}\selectfont Numéro d'étudiant. \secondauthornum}
	\fi

    \ifdefined\thirdauthorname
        \vspace{20pt}
        {\noindent\bfseries\fontsize{14}{19}\selectfont\thirdauthorname} \\
        {\noindent\itshape\fontsize{10}{12}\selectfont Student No. \thirdauthornum}
	\fi

    \vfill

    {
    \noindent
    \latofont
    \fontsize{10}{12}\selectfont
    \renewcommand{\arraystretch}{0.1}
    \hspace*{-5.5pt}\begin{tabular}{@{}r@{\hspace{5pt}}>{\raggedright\arraybackslash}m{6cm}@{}}
        \ifdefined\sprofname
            \textbf{Professors:} & \fprofname \\ [-.7ex]
        \else
            \textbf{Professeur:} & \fprofname \\ [-.7ex]
        \fi
        & \setstretch{0.9}{\fontsize{8}{10}\selectfont\itshape \fproftitle} \\ [.5ex]
        
        \ifdefined\sprofname
            & \sprofname \\ [-.7ex]
            & \setstretch{0.9}{\fontsize{8}{10}\selectfont\itshape \sproftitle} \\ [.5ex]
        \fi

        \ifdefined\tprofname
            & \tprofname \\ [-.7ex]
            & \setstretch{0.9}{\fontsize{8}{10}\selectfont\itshape \tproftitle} \\
        \fi
    \end{tabular}
    }
    
    \vfill

    % School
	{\noindent\fontsize{10}{12}\selectfont\schoolname}
	
    % Department
	{\noindent\fontsize{10}{12}\selectfont\departmentname}

    % Degree
	{\noindent\fontsize{10}{12}\selectfont\degname}

    % Course
    \ifdefined\coursename
        {\noindent\fontsize{10}{12}\selectfont\coursename}
	\fi

    \vspace{70pt}

    % Local & Date
	{\noindent\fontsize{10}{12}\selectfont\thedate}

    \vspace{68pt}
\end{titlepage}
\restoregeometry