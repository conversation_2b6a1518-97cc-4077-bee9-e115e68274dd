\chapter{Custom Notes}
\label{cp:notes}

In the course of writing, it is sometimes necessary to insert notes in the middle of the document so that in the future we can return to them and read what we have written. Sometimes we need a space to jot down a quick \verb|TODO|. With this in mind, two commands have been developed: \verb|\todo{TEXT}| and \verb|\note{TEXT}|. These commands accept normal text and produce two different types of boxes, respectively, for each type of use. These boxes are ``breakable'', which means that they can be placed anywhere in the document and will behave as if they were text. Below is a simple usage of both environments. Please note that both of these environments were created with the intention of helping you develop your work and \textbf{should not be used in the final version of your document!}

\todo{Write about X! Later, we should remove Y.}

\note{Come to me, all you who are weary and burdened, and I will give you rest. Take my yoke upon you and learn from me, for I am gentle and humble in heart, and you will find rest for your souls.}
