@article{arb<PERSON>2000windows,
  title={Windows of vulnerability: A case study analysis},
  author={<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON><PERSON><PERSON>, <PERSON>},
  journal={Computer},
  volume={33},
  number={12},
  pages={52--59},
  year={2000},
  publisher={IEEE}
}


@misc{zenodo3559480,
  author       = {<PERSON>},
  title        = {VUDENC - python corpus for Word2Vec},
  year         = {2019},
  howpublished = {\url{https://zenodo.org/records/3559480}},
  doi          = {10.5281/zenodo.3559480},
  note         = {Accessed: 21-11-2024},
  supervisor   = {<PERSON><PERSON><PERSON>, <PERSON>},
  description  = {Python corpus for training a Word2Vec model, and one trained model.}
}


@misc{zenodo3559841,
  author       = {<PERSON>},
  title        = {VUDENC - dataset with diff files},
  year         = {2019},
  howpublished = {Zenodo},
  doi          = {10.5281/zenodo.3559841},
  note         = {Accessed: 21-11-2024},
  supervisor   = {<PERSON><PERSON><PERSON>, <PERSON>},
  description  = {The dataset of commits including their diff files gathered from GitHub.}
}

@misc{zenodo35598412,
  author       = {<PERSON> },
  title        = {VUDENC - datasets for vulnerabilities},
  year         = {2019},
  howpublished = {\url{https://zenodo.org/records/3559841}},
  doi          = {10.5281/zenodo.3559841},
  note         = {Accessed: 21-11-2024}
}



@article{10.1145/3699711,
author = {Shiri Harzevili, Nima and Boaye Belle, Alvine and Wang, Junjie and Wang, Song and Jiang, Zhen Ming (Jack) and Nagappan, Nachiappan},
title = {A Systematic Literature Review on Automated Software Vulnerability Detection Using Machine Learning},
year = {2024},
issue_date = {March 2025},
publisher = {Association for Computing Machinery},
address = {New York, NY, USA},
volume = {57},
number = {3},
issn = {0360-0300},
url = {https://doi.org/10.1145/3699711},
doi = {10.1145/3699711},
abstract = {In recent years, numerous Machine Learning (ML) models, including Deep Learning (DL) and classic ML models, have been developed to detect software vulnerabilities. However, there is a notable lack of comprehensive and systematic surveys that summarize, classify, and analyze the applications of these ML models in software vulnerability detection. This absence may lead to critical research areas being overlooked or under-represented, resulting in a skewed understanding of the current state of the art in software vulnerability detection. To close this gap, we propose a comprehensive and systematic literature review that characterizes the different properties of ML-based software vulnerability detection systems using six major Research Questions (RQs).Using a custom web scraper, our systematic approach involves extracting a set of studies from four widely used online digital libraries: ACM Digital Library, IEEE Xplore, ScienceDirect, and Google Scholar. We manually analyzed the extracted studies to filter out irrelevant work unrelated to software vulnerability detection, followed by creating taxonomies and addressing RQs. Our analysis indicates a significant upward trend in applying ML techniques for software vulnerability detection over the past few years, with many studies published in recent years. Prominent conference venues include the International Conference on Software Engineering (ICSE), the International Symposium on Software Reliability Engineering (ISSRE), the Mining Software Repositories (MSR) conference, and the ACM International Conference on the Foundations of Software Engineering (FSE), whereas Information and Software Technology (IST), Computers \& Security (C&S), and Journal of Systems and Software (JSS) are the leading journal venues.Our results reveal that 39.1\% of the subject studies use hybrid sources, whereas 37.6\% of the subject studies utilize benchmark data for software vulnerability detection. Code-based data are the most commonly used data type among subject studies, with source code being the predominant subtype. Graph-based and token-based input representations are the most popular techniques, accounting for 57.2\% and 24.6\% of the subject studies, respectively. Among the input embedding techniques, graph embedding and token vector embedding are the most frequently used techniques, accounting for 32.6\% and 29.7\% of the subject studies. Additionally, 88.4\% of the subject studies use DL models, with recurrent neural networks and graph neural networks being the most popular subcategories, whereas only 7.2\% use classic ML models. Among the vulnerability types covered by the subject studies, CWE-119, CWE-20, and CWE-190 are the most frequent ones. In terms of tools used for software vulnerability detection, Keras with TensorFlow backend and PyTorch libraries are the most frequently used model-building tools, accounting for 42 studies for each. In addition, Joern is the most popular tool used for code representation, accounting for 24 studies.Finally, we summarize the challenges and future directions in the context of software vulnerability detection, providing valuable insights for researchers and practitioners in the field.},
journal = {ACM Comput. Surv.},
month = nov,
articleno = {55},
numpages = {36},
keywords = {Source code, software security, software vulnerability detection, software bug detection, machine learning, deep learning}
}

@article{mikolov2013efficient,
  title={Efficient Estimation of Word Representations in Vector Space},
  author={Mikolov, Tomas and Chen, Kai and Corrado, Greg and Dean, Jeffrey},
  journal={arXiv preprint arXiv:1301.3781},
  year={2013}
}

@article{DBLP:journals/corr/abs-2201-08441,
  author       = {Laura Wartschinski and
                  Yannic Noller and
                  Thomas Vogel and
                  Timo Kehrer and
                  Lars Grunske},
  title        = {{VUDENC:} Vulnerability Detection with Deep Learning on a Natural
                  Codebase for Python},
  journal      = {CoRR},
  volume       = {abs/2201.08441},
  year         = {2022},
  url          = {https://arxiv.org/abs/2201.08441},
  eprinttype    = {arXiv},
  eprint       = {2201.08441},
  timestamp    = {Tue, 01 Feb 2022 14:59:01 +0100},
  biburl       = {https://dblp.org/rec/journals/corr/abs-2201-08441.bib},
  bibsource    = {dblp computer science bibliography, https://dblp.org}
}

@dataset{wartschinski_2019_3559480,
  author       = {Wartschinski, Laura},
  title        = {VUDENC - python corpus for word2vec},
  month        = dec,
  year         = 2019,
  publisher    = {Zenodo},
  doi          = {10.5281/zenodo.3559480},
  url          = {https://doi.org/10.5281/zenodo.3559480}
}

@article{hochreiter1997lstm,
  title={Long short-term memory},
  author={Hochreiter, Sepp and Schmidhuber, J{\"u}rgen},
  journal={Neural computation},
  volume={9},
  number={8},
  pages={1735--1780},
  year={1997},
  publisher={MIT Press}
}

@dataset{wartschinski_2019_3559841,
  author       = {Wartschinski, Laura},
  title        = {VUDENC - datasets for vulnerabilities},
  month        = dec,
  year         = 2019,
  publisher    = {Zenodo},
  doi          = {10.5281/zenodo.3559841},
  url          = {https://doi.org/10.5281/zenodo.3559841}
}

@inproceedings{10.1145/2663716.2663755,
author = {Durumeric, Zakir and Li, Frank and Kasten, James and Amann, Johanna and Beekman, Jethro and Payer, Mathias and Weaver, Nicolas and Adrian, David and Paxson, Vern and Bailey, Michael and Halderman, J. Alex},
title = {The Matter of Heartbleed},
year = {2014},
isbn = {9781450332132},
publisher = {Association for Computing Machinery},
address = {New York, NY, USA},
url = {https://doi.org/10.1145/2663716.2663755},
doi = {10.1145/2663716.2663755},
abstract = {The Heartbleed vulnerability took the Internet by surprise in April 2014. The vulnerability, one of the most consequential since the advent of the commercial Internet, allowed attackers to remotely read protected memory from an estimated 24--55\% of popular HTTPS sites. In this work, we perform a comprehensive, measurement-based analysis of the vulnerability's impact, including (1) tracking the vulnerable population, (2) monitoring patching behavior over time, (3) assessing the impact on the HTTPS certificate ecosystem, and (4) exposing real attacks that attempted to exploit the bug. Furthermore, we conduct a large-scale vulnerability notification experiment involving 150,000 hosts and observe a nearly 50\% increase in patching by notified hosts. Drawing upon these analyses, we discuss what went well and what went poorly, in an effort to understand how the technical community can respond more effectively to such events in the future.},
booktitle = {Proceedings of the 2014 Conference on Internet Measurement Conference},
pages = {475–488},
numpages = {14},
keywords = {security, openssl, internet-wide scanning, heartbleed},
location = {Vancouver, BC, Canada},
series = {IMC '14}
}

@inproceedings{10.1145/3236024.3264598,
author = {Spadini, Davide and Aniche, Maur\'{\i}cio and Bacchelli, Alberto},
title = {PyDriller: Python framework for mining software repositories},
year = {2018},
isbn = {9781450355735},
publisher = {Association for Computing Machinery},
address = {New York, NY, USA},
url = {https://doi.org/10.1145/3236024.3264598},
doi = {10.1145/3236024.3264598},
abstract = {Software repositories contain historical and valuable information about the overall development of software systems. Mining software repositories (MSR) is nowadays considered one of the most interesting growing fields within software engineering. MSR focuses on extracting and analyzing data available in software repositories to uncover interesting, useful, and actionable information about the system. Even though MSR plays an important role in software engineering research, few tools have been created and made public to support developers in extracting information from Git repository. In this paper, we present PyDriller, a Python Framework that eases the process of mining Git. We compare our tool against the state-of-the-art Python Framework GitPython, demonstrating that PyDriller can achieve the same results with, on average, 50\% less LOC and significantly lower complexity.  URL: https://github.com/ishepard/pydriller  Materials: https://doi.org/10.5281/zenodo.1327363  Pre-print: https://doi.org/10.5281/zenodo.1327411},
booktitle = {Proceedings of the 2018 26th ACM Joint Meeting on European Software Engineering Conference and Symposium on the Foundations of Software Engineering},
pages = {908–911},
numpages = {4},
keywords = {Python, Mining Software Repositories, GitPython, Git},
location = {Lake Buena Vista, FL, USA},
series = {ESEC/FSE 2018}
}

@inproceedings{inproceedings,
author = {Pennington, Jeffrey and Socher, Richard and Manning, Christopher},
year = {2014},
month = {01},
pages = {1532-1543},
title = {Glove: Global Vectors for Word Representation},
volume = {14},
journal = {EMNLP},
doi = {10.3115/v1/D14-1162}
}

@article{DBLP:journals/corr/BojanowskiGJM16,
  author       = {Piotr Bojanowski and
                  Edouard Grave and
                  Armand Joulin and
                  Tom{\'{a}}s Mikolov},
  title        = {Enriching Word Vectors with Subword Information},
  journal      = {CoRR},
  volume       = {abs/1607.04606},
  year         = {2016},
  url          = {http://arxiv.org/abs/1607.04606},
  eprinttype    = {arXiv},
  eprint       = {1607.04606},
  timestamp    = {Mon, 28 Dec 2020 11:31:02 +0100},
  biburl       = {https://dblp.org/rec/journals/corr/BojanowskiGJM16.bib},
  bibsource    = {dblp computer science bibliography, https://dblp.org}
}

@article{DBLP:journals/corr/abs-1810-04805,
  author       = {Jacob Devlin and
                  Ming{-}Wei Chang and
                  Kenton Lee and
                  Kristina Toutanova},
  title        = {{BERT:} Pre-training of Deep Bidirectional Transformers for Language
                  Understanding},
  journal      = {CoRR},
  volume       = {abs/1810.04805},
  year         = {2018},
  url          = {http://arxiv.org/abs/1810.04805},
  eprinttype    = {arXiv},
  eprint       = {1810.04805},
  timestamp    = {Tue, 30 Oct 2018 20:39:56 +0100},
  biburl       = {https://dblp.org/rec/journals/corr/abs-1810-04805.bib},
  bibsource    = {dblp computer science bibliography, https://dblp.org}
}

@book{géron2019hands,
  title={Hands-on Machine Learning with Scikit-Learn, Keras, and TensorFlow: Concepts, Tools, and Techniques to Build Intelligent Systems},
  author={G{\'e}ron, A.},
  isbn={9781492032649},
  url={https://books.google.fr/books?id=OCS1twEACAAJ},
  year={2019},
  publisher={O'Reilly Media, Incorporated}
}

@article{DBLP:journals/corr/VaswaniSPUJGKP17,
  author       = {Ashish Vaswani and
                  Noam Shazeer and
                  Niki Parmar and
                  Jakob Uszkoreit and
                  Llion Jones and
                  Aidan N. Gomez and
                  Lukasz Kaiser and
                  Illia Polosukhin},
  title        = {Attention Is All You Need},
  journal      = {CoRR},
  volume       = {abs/1706.03762},
  year         = {2017},
  url          = {http://arxiv.org/abs/1706.03762},
  eprinttype    = {arXiv},
  eprint       = {1706.03762},
  timestamp    = {Sat, 23 Jan 2021 01:20:40 +0100},
  biburl       = {https://dblp.org/rec/journals/corr/VaswaniSPUJGKP17.bib},
  bibsource    = {dblp computer science bibliography, https://dblp.org}
}

@inproceedings{DBLP:journals/corr/KingmaB14,
  author       = {Diederik P. Kingma and
                  Jimmy Ba},
  editor       = {Yoshua Bengio and
                  Yann LeCun},
  title        = {Adam: {A} Method for Stochastic Optimization},
  booktitle    = {3rd International Conference on Learning Representations, {ICLR} 2015,
                  San Diego, CA, USA, May 7-9, 2015, Conference Track Proceedings},
  year         = {2015},
  url          = {http://arxiv.org/abs/1412.6980},
  timestamp    = {Thu, 25 Jul 2019 14:25:37 +0200},
  biburl       = {https://dblp.org/rec/journals/corr/KingmaB14.bib},
  bibsource    = {dblp computer science bibliography, https://dblp.org}
}

@misc{wartschinski_vulnerability_2023,
  author       = {Laura Wartschinski},
  title        = {VulnerabilityDetection},
  year         = {2023},
  howpublished = {\url{https://github.com/LauraWartschinski/VulnerabilityDetection}},
  note         = {Accessed: 2024-12-17}
}

@article{DBLP:journals/corr/abs-2001-02334,
  author       = {Deqing Zou and
                  Sujuan Wang and
                  Shouhuai Xu and
                  Zhen Li and
                  Hai Jin},
  title        = {{\(\mu\)}VulDeePecker: {A} Deep Learning-Based System for Multiclass
                  Vulnerability Detection},
  journal      = {CoRR},
  volume       = {abs/2001.02334},
  year         = {2020},
  url          = {http://arxiv.org/abs/2001.02334},
  eprinttype    = {arXiv},
  eprint       = {2001.02334},
  timestamp    = {Tue, 04 May 2021 14:14:56 +0200},
  biburl       = {https://dblp.org/rec/journals/corr/abs-2001-02334.bib},
  bibsource    = {dblp computer science bibliography, https://dblp.org}
}