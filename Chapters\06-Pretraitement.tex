\titlespacing*{\chapter}{0pt}{-10pt}{40pt}
\chapter{Prétraitement}
\label{cp:pretraitement}
Le prétraitement des données brutes est essentiel pour préparer le code source à l'analyse par le modèle d'apprentissage profond. Tout d'abord, le code est soumis à une tokenisation, où il est décomposé en une séquence de tokens tels que les mots-clés, les identificateurs, les littéraux et les opérateurs. Cette étape simplifie le code en éléments fondamentaux, facilitant son traitement ultérieur.\\

\noindent Ensuite, les tokens sont vectorisés à l'aide de l'algorithme word2vec, qui convertit chaque token en un vecteur numérique en capturant les relations sémantiques entre eux. Cela permet de représenter le code dans un espace vectoriel adapté à l'apprentissage automatique, où les similitudes sémantiques sont préservées.\\

\noindent Pour traiter correctement les données et éviter un ensemble de données déséquilibré, il est nécessaire de gérer proportionnellement les parties vulnérables et non vulnérables lors de l'étape d'étiquetage. Les tokens sont regroupés en fragments de code de taille configurable, fournissant au modèle le contexte nécessaire pour comprendre les structures du code lors de l'analyse. L'idée est de diviser les données en blocs ; un bloc est étiqueté comme vulnérable s'il chevauche un segment de code vulnérable, sinon il est considéré comme sain. Cette approche assure un équilibre dans le jeu de données entre les exemples positifs et négatifs, ce qui est crucial pour la performance et la fiabilité du modèle.